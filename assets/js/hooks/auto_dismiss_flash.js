const AutoDismissFlash = {
  mounted() {
    this.startCountdown()
  },

  startCountdown() {
    // Get the countdown duration (default 5 seconds)
    const duration = parseInt(this.el.dataset.duration || '5')
    let timeLeft = duration
    
    // Find the countdown element
    const countdownElement = this.el.querySelector('.countdown')
    if (!countdownElement) return

    // Set initial countdown value
    this.updateCountdown(countdownElement, timeLeft)
    
    // Start the countdown timer
    this.timer = setInterval(() => {
      timeLeft--
      this.updateCountdown(countdownElement, timeLeft)
      
      if (timeLeft <= 0) {
        this.dismissFlash()
      }
    }, 1000)

    // Pause countdown on hover
    this.el.addEventListener('mouseenter', () => {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    })

    // Resume countdown when mouse leaves (restart from current time)
    this.el.addEventListener('mouseleave', () => {
      if (!this.timer && timeLeft > 0) {
        this.timer = setInterval(() => {
          timeLeft--
          this.updateCountdown(countdownElement, timeLeft)
          
          if (timeLeft <= 0) {
            this.dismissFlash()
          }
        }, 1000)
      }
    })
  },

  updateCountdown(element, timeLeft) {
    // Update the countdown display using DaisyUI countdown format
    element.style.setProperty('--value', timeLeft)
  },

  dismissFlash() {
    // Clear the timer
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // Trigger the existing flash dismiss functionality
    const dismissButton = this.el.querySelector('button[aria-label="close"]')
    if (dismissButton) {
      dismissButton.click()
    }
  },

  destroyed() {
    // Clean up timer when component is destroyed
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  }
}

export default AutoDismissFlash
