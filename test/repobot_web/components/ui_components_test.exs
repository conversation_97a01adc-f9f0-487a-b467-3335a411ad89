defmodule RepobotWeb.UI.ComponentsTest do
  use RepobotWeb.ConnCase, async: true
  import Phoenix.LiveViewTest
  import Phoenix.Component
  import RepobotWeb.UI.Components
  import RepobotWeb.CoreComponents

  describe "btn/1" do
    test "renders button element when href is not provided" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.btn variant="primary">Click me</.btn>
        """)

      assert html =~ "<button"
      assert html =~ "btn btn-primary"
      assert html =~ "Click me"
      refute html =~ "<a"
    end

    test "renders anchor element when href is provided" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.btn href="/test" variant="primary">Click me</.btn>
        """)

      assert html =~ "<a"
      assert html =~ ~s|href="/test"|
      assert html =~ "btn btn-primary"
      assert html =~ "Click me"
      refute html =~ "<button"
    end

    test "preserves custom classes and attributes for anchor elements" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.btn href="/test" variant="secondary" class="custom-class" data-phx-link="redirect">
          <span>Test</span>
        </.btn>
        """)

      assert html =~ "<a"
      assert html =~ ~s|href="/test"|
      assert html =~ "btn btn-secondary"
      assert html =~ "custom-class"
      assert html =~ ~s|data-phx-link="redirect"|
      assert html =~ "<span>Test</span>"
    end

    test "preserves icon rendering in anchor elements" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.btn href="/edit" variant="secondary" class="inline-flex items-center px-3 py-2">
          <.icon name="hero-pencil" class="w-4 h-4 mr-2 text-indigo-600" /> Edit
        </.btn>
        """)

      assert html =~ "<a"
      assert html =~ ~s|href="/edit"|
      assert html =~ "btn btn-secondary"
      assert html =~ "inline-flex items-center px-3 py-2"
      assert html =~ "hero-pencil"
      assert html =~ "w-4 h-4 mr-2 text-indigo-600"
      assert html =~ "Edit"
    end

    test "handles disabled state for button elements" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.btn disabled variant="primary">Disabled</.btn>
        """)

      assert html =~ "<button"
      assert html =~ "btn btn-primary"
      assert html =~ "btn-disabled"
      assert html =~ "disabled"
      assert html =~ "Disabled"
    end

    test "supports all DaisyUI variants" do
      variants =
        ~w(default primary secondary accent info success warning error outline ghost soft)

      for variant <- variants do
        assigns = %{variant: variant}

        html =
          rendered_to_string(~H"""
          <.btn variant={@variant}>Test</.btn>
          """)

        expected_class =
          case variant do
            "default" -> "btn"
            _ -> "btn btn-#{variant}"
          end

        assert html =~ expected_class
      end
    end
  end

  describe "enhanced_flash/1" do
    test "renders info flash with semi-transparent styling" do
      assigns = %{flash: %{"info" => "Success message"}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:info} flash={@flash} />
        """)

      assert html =~ "toast toast-top toast-end z-50"
      assert html =~ "alert-success bg-success/80 border-success/30"
      assert html =~ "backdrop-blur-sm"
      assert html =~ "Success message"
      assert html =~ "hero-information-circle-mini"
    end

    test "renders error flash with semi-transparent styling" do
      assigns = %{flash: %{"error" => "Error message"}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:error} flash={@flash} />
        """)

      assert html =~ "toast toast-top toast-end z-50"
      assert html =~ "alert-error bg-error/80 border-error/30"
      assert html =~ "backdrop-blur-sm"
      assert html =~ "Error message"
      assert html =~ "hero-exclamation-circle-mini"
    end

    test "renders with custom title" do
      assigns = %{flash: %{"info" => "Message content"}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:info} title="Custom Title" flash={@flash} />
        """)

      assert html =~ "Custom Title"
      assert html =~ "Message content"
      assert html =~ "font-semibold"
    end

    test "renders with inner block content" do
      assigns = %{}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:info}>
          <span>Custom content</span>
        </.enhanced_flash>
        """)

      assert html =~ "<span>Custom content</span>"
      assert html =~ "alert-success bg-success/80 border-success/30"
    end

    test "does not render when no flash message" do
      assigns = %{flash: %{}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:info} flash={@flash} />
        """)

      assert html == ""
    end

    test "includes close button with enhanced opacity styling" do
      assigns = %{flash: %{"info" => "Test message"}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:info} flash={@flash} />
        """)

      assert html =~ "btn btn-ghost btn-sm btn-square opacity-70 hover:opacity-100"
      assert html =~ "hero-x-mark-solid"
      assert html =~ ~s|aria-label="close"|
    end

    test "includes proper JavaScript for dismissing flash" do
      assigns = %{flash: %{"error" => "Test error"}}

      html =
        rendered_to_string(~H"""
        <.enhanced_flash kind={:error} flash={@flash} />
        """)

      assert html =~ ~s|phx-click=|
      assert html =~ ~s|lv:clear-flash|
      assert html =~ ~s|role="alert"|
    end
  end
end
